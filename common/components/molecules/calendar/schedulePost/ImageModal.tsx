'use client'

import { CanvasEditor } from '@/common/components/organisms/canvasEditor/CanvasEditor';
import {
  useState, useEffect,
} from 'react';
import toast from 'react-hot-toast';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  planId: string;
  content?: string;
  onImageAttached: (imageUrl: string, isFromAI: boolean, file?: File) => void;
  initialImage?: string;
  platform?: string;
}

export const ImageModal = ({
  isOpen,
  onClose,
  agentId,
  planId,
  onImageAttached,
  initialImage,
  platform,
}: ImageModalProps) => {
  const [currentImage, setCurrentImage] = useState(initialImage || '');

  useEffect(() => {
    setCurrentImage(initialImage || '');
  }, [initialImage]);

  // Reset currentImage when modal closes to ensure fresh state on next open
  useEffect(() => {
    if (!isOpen) {
      setCurrentImage('');
    }
  }, [isOpen]);

  const handleCanvasEditorSave = async (imageUrl: string) => {
    if (imageUrl) {
      try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const timestamp = Date.now();
        const fileName = `canvas-design-${timestamp}.png`;
        const file = new File([blob], fileName, { type: 'image/png' });

        onImageAttached(imageUrl, false, file);
        setCurrentImage(imageUrl);
        onClose();
        toast.success('Media saved!');
      } catch (error) {
        console.error('Error processing canvas image:', error);
        toast.error('Failed to save media');
      }
    } else {
      onClose();
    }
  };

  return (
    <CanvasEditor
      isOpen={isOpen}
      onClose={() => onClose()}
      onSave={handleCanvasEditorSave}
      initialImage={currentImage || undefined}
      agentId={agentId}
      planId={planId}
      platform={platform}
    />
  );
};

export default ImageModal;
